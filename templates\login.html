<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Guest Genius - Secure Login</title>

    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://rsms.me https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://rsms.me; img-src 'self' data:;">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.7.6/dist/css/uikit.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.7.6/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.7.6/dist/js/uikit-icons.min.js"></script>
    {% include 'imports.html' %}
    <style>
        /* Security and performance optimizations */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .pure-black .login-button {
            background-color: #4F46E5;
            color: #fff;
        }

        .pure-black .login-button:hover {
            background-color: #403ac2;
            color: #fff;
        }

        /* Enhanced autofill style overrides */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            -webkit-text-fill-color: inherit !important;
            transition: background-color 5000s ease-in-out 0s;
            background-color: transparent !important;
            background: transparent !important;
            caret-color: inherit;
        }

        /* For Firefox and other browsers */
        input:autofill {
            background-color: transparent !important;
            color: inherit !important;
            appearance: none !important;
        }

        /* Dark mode specific overrides */
        .dark-mode input:-webkit-autofill,
        .pure-black input:-webkit-autofill,
        .dark input:-webkit-autofill {
            -webkit-text-fill-color: #fff !important;
            background-color: transparent !important;
            color: #fff !important;
        }

        /* Enforce transparent backgrounds on inputs */
        .dark-mode input,
        .pure-black input,
        .dark input {
            background-color: transparent !important;
            color: inherit !important;
        }

        /* Loading state */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Error message styling */
        .error-message {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Security indicator */
        .security-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: #10b981;
            margin-top: 0.5rem;
        }

        /* Rate limit warning */
        .rate-limit-warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }
    </style>

</head>

<body>
    <div class="flex min-h-screen flex-col justify-center px-6 py-12 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-sm">
            <!-- Security indicator -->
            <div class="security-indicator justify-center mb-4">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
                <span>Secure Login</span>
            </div>

            <h3 class="text-center text-2xl/9 font-bold tracking-tight">Sign in to your account</h3>
        </div>

        <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
            <!-- Error message display -->
            {% if error %}
            <div class="error-message text-center text-red-600 mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {{ error }}
            </div>
            {% endif %}

            <!-- Login form with enhanced security -->
            <form id="loginForm" class="space-y-6" action="/login" method="POST" autocomplete="off" novalidate>
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                <div>
                    <label for="username" class="block text-sm/6 font-medium">Username</label>
                    <div class="mt-2">
                        <input
                            type="text"
                            name="username"
                            id="username"
                            autocomplete="username"
                            required
                            maxlength="100"
                            pattern="[a-zA-Z0-9._@-]+"
                            title="Username can only contain letters, numbers, dots, underscores, @ and hyphens"
                            class="block w-full rounded-md px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                            placeholder="Enter your username">
                    </div>
                </div>

                <div>
                    <div class="flex items-center justify-between">
                        <label for="password" class="block text-sm/6 font-medium">Password</label>
                        <div class="text-sm">
                            <a href="#" class="font-semibold text-indigo-600 hover:text-indigo-500" onclick="showPasswordHelp()">Need help?</a>
                        </div>
                    </div>
                    <div class="mt-2">
                        <input
                            type="password"
                            name="password"
                            id="password"
                            autocomplete="current-password"
                            required
                            minlength="8"
                            maxlength="128"
                            class="block w-full rounded-md px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                            placeholder="Enter your password">
                    </div>
                </div>

                <div>
                    <button
                        type="submit"
                        id="submitBtn"
                        class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm/6 font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 login-button transition-colors duration-200">
                        <span id="submitText">Sign in</span>
                        <svg id="loadingSpinner" class="hidden animate-spin ml-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </form>

            <p class="mt-10 text-center text-sm/6 text-gray-500">
                Not a member?
                <a href="#" style="margin-left: 5px;" class="font-semibold text-indigo-600 hover:text-indigo-500">Reach out to us today!</a>
            </p>

            <!-- Security notice -->
            <div class="mt-6 text-center text-xs text-gray-400">
                <p>🔒 Your connection is secure and encrypted</p>
            </div>
        </div>
    </div>
    <script>
        // Security and UX enhancements
        let loginAttempts = 0;
        const maxAttempts = 5;
        let isSubmitting = false;

        // Form validation and security
        function validateForm() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showError('Please fill in all fields');
                return false;
            }

            if (username.length < 3 || username.length > 100) {
                showError('Username must be between 3 and 100 characters');
                return false;
            }

            if (password.length < 8) {
                showError('Password must be at least 8 characters long');
                return false;
            }

            // Basic XSS prevention
            const xssPattern = /<script|javascript:|on\w+=/i;
            if (xssPattern.test(username) || xssPattern.test(password)) {
                showError('Invalid characters detected');
                return false;
            }

            return true;
        }

        function showError(message) {
            // Remove existing error messages
            const existingError = document.querySelector('.error-message');
            if (existingError && !existingError.classList.contains('mb-4')) {
                existingError.remove();
            }

            // Create new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-center text-red-600 mb-4 p-3 bg-red-50 border border-red-200 rounded-md';
            errorDiv.innerHTML = `
                <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${message}
            `;

            const form = document.getElementById('loginForm');
            form.parentNode.insertBefore(errorDiv, form);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        function showLoading(show) {
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingSpinner = document.getElementById('loadingSpinner');

            if (show) {
                submitBtn.disabled = true;
                submitBtn.classList.add('loading');
                submitText.textContent = 'Signing in...';
                loadingSpinner.classList.remove('hidden');
            } else {
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');
                submitText.textContent = 'Sign in';
                loadingSpinner.classList.add('hidden');
            }
        }

        function showPasswordHelp() {
            alert('If you need password assistance, please contact your administrator.');
        }

        // Enhanced form submission with security
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            if (isSubmitting) return;

            if (!validateForm()) return;

            if (loginAttempts >= maxAttempts) {
                showError('Too many failed attempts. Please wait before trying again.');
                return;
            }

            isSubmitting = true;
            showLoading(true);

            const formData = new FormData(e.target);

            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin', // Include cookies
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest' // CSRF protection
                    }
                });

                if (response.status === 429) {
                    const data = await response.json();
                    showError(`Too many requests. Please try again in ${Math.ceil(data.retry_after / 60)} minutes.`);
                    return;
                }

                if (response.status === 423) {
                    const data = await response.json();
                    showError(data.error || 'Account temporarily locked');
                    return;
                }

                if (response.ok) {
                    // Success - redirect will happen automatically for non-JSON requests
                    if (response.headers.get('content-type')?.includes('application/json')) {
                        const result = await response.json();
                        if (result.message) {
                            // Successful login via API
                            window.location.href = '/';
                        }
                    } else {
                        // Redirect response
                        window.location.href = '/';
                    }
                } else {
                    loginAttempts++;
                    const errorText = await response.text();

                    // Try to parse as JSON first
                    try {
                        const errorData = JSON.parse(errorText);
                        showError(errorData.error || 'Login failed');
                    } catch {
                        // If not JSON, check if it's HTML with error
                        if (errorText.includes('Invalid username or password')) {
                            showError('Invalid username or password');
                        } else {
                            showError('Login failed. Please try again.');
                        }
                    }
                }
            } catch (err) {
                console.error('Login error:', err);
                showError('Network error. Please check your connection and try again.');
            } finally {
                isSubmitting = false;
                showLoading(false);
            }
        });

        // Clear error messages when user starts typing
        document.getElementById('username').addEventListener('input', () => {
            const errorMsg = document.querySelector('.error-message');
            if (errorMsg && !errorMsg.classList.contains('mb-4')) {
                errorMsg.remove();
            }
        });

        document.getElementById('password').addEventListener('input', () => {
            const errorMsg = document.querySelector('.error-message');
            if (errorMsg && !errorMsg.classList.contains('mb-4')) {
                errorMsg.remove();
            }
        });

        // Prevent form resubmission on page refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }

        // Focus on username field when page loads
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('username').focus();
        });
    </script>
</body>

</html>
