from flask import Blueprint, render_template, request, redirect, url_for, session, jsonify, make_response, flash
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
import os
import jwt
import datetime
import secrets
import time
import re
import hashlib
from datetime import timed<PERSON><PERSON>
from dotenv import load_dotenv
from collections import defaultdict, deque

auth = Blueprint('auth', __name__)

# Load environment variables once at startup
load_dotenv()

# Get admin credentials from environment variables
ADMIN_USERNAME = os.getenv('ADMIN_USERNAME', 'admin')
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin12345')
JWT_SECRET = os.getenv('JWT_SECRET', secrets.token_urlsafe(32))  # Generate secure secret if not provided
JWT_EXPIRATION = int(os.getenv('JWT_EXPIRATION', '3600'))  # Default 1 hour

# Security configurations
MAX_LOGIN_ATTEMPTS = 5
LOCKOUT_DURATION = 900  # 15 minutes in seconds
RATE_LIMIT_WINDOW = 300  # 5 minutes in seconds
MAX_REQUESTS_PER_WINDOW = 10

# Initialize user database with admin credentials
users = {
    ADMIN_USERNAME: generate_password_hash(ADMIN_PASSWORD),
}

# Rate limiting and security tracking
login_attempts = defaultdict(lambda: deque())
failed_attempts = defaultdict(int)
locked_accounts = {}
request_tracking = defaultdict(lambda: deque())


def get_client_ip():
    """Get the real client IP address"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    return request.remote_addr

def is_rate_limited(identifier):
    """Check if the identifier (IP/username) is rate limited"""
    current_time = time.time()
    requests = request_tracking[identifier]

    # Remove old requests outside the window
    while requests and requests[0] < current_time - RATE_LIMIT_WINDOW:
        requests.popleft()

    # Check if rate limit exceeded
    return len(requests) >= MAX_REQUESTS_PER_WINDOW

def record_request(identifier):
    """Record a request for rate limiting"""
    current_time = time.time()
    request_tracking[identifier].append(current_time)

def is_account_locked(username):
    """Check if account is locked due to failed attempts"""
    if username in locked_accounts:
        lock_time = locked_accounts[username]
        if time.time() - lock_time < LOCKOUT_DURATION:
            return True
        else:
            # Lock expired, remove it
            del locked_accounts[username]
            failed_attempts[username] = 0
    return False

def record_failed_attempt(username):
    """Record a failed login attempt"""
    failed_attempts[username] += 1
    if failed_attempts[username] >= MAX_LOGIN_ATTEMPTS:
        locked_accounts[username] = time.time()

def validate_password_strength(password):
    """Validate password meets security requirements"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "Password must contain at least one special character"
    return True, "Password is strong"

def sanitize_input(input_string):
    """Basic input sanitization"""
    if not input_string:
        return ""
    # Remove potential XSS characters
    sanitized = re.sub(r'[<>"\']', '', str(input_string).strip())
    return sanitized[:100]  # Limit length

def generate_csrf_token():
    """Generate a CSRF token"""
    return secrets.token_urlsafe(32)

def generate_token(username):
    """Generate a JWT token for the user"""
    print("GENERATING A TOKEN")
    payload = {
        'username': username,
        'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=JWT_EXPIRATION),
        'iat': datetime.datetime.now(datetime.timezone.utc),
        'jti': secrets.token_urlsafe(16)  # Unique token ID
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Get token from Authorization header
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        # Check for token in cookies (for web browser flows)
        if not token and request.cookies.get('token'):
            token = request.cookies.get('token')

        # Alternatively, check if token is in request parameters
        if not token:
            token = request.args.get('token')

        if not token:
            return jsonify({'message': 'Token is missing!'}), 401

        try:
            data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            # You could fetch user details here if needed
            current_user = data['username']
        except:
            return jsonify({'message': 'Token is invalid!'}), 401

        return f(current_user=current_user, *args, **kwargs)

    return decorated

@auth.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'GET':
        # Generate CSRF token for the form
        csrf_token = generate_csrf_token()
        session['csrf_token'] = csrf_token
        return render_template('login.html', csrf_token=csrf_token)

    # POST request handling
    client_ip = get_client_ip()

    # Check rate limiting
    if is_rate_limited(client_ip):
        return jsonify({
            'error': 'Too many requests. Please try again later.',
            'retry_after': RATE_LIMIT_WINDOW
        }), 429

    # Record this request
    record_request(client_ip)

    # Get and sanitize form data
    username = sanitize_input(request.form.get('username', ''))
    password = request.form.get('password', '')
    csrf_token = request.form.get('csrf_token', '')

    # Check if request wants JSON response (API call)
    wants_json = request.headers.get('Accept') == 'application/json' or request.headers.get('Content-Type') == 'application/json'

    # Validate CSRF token
    if not csrf_token or csrf_token != session.get('csrf_token'):
        error_msg = 'Invalid security token. Please refresh and try again.'
        if wants_json:
            return jsonify({'error': error_msg}), 403
        return render_template('login.html', error=error_msg, csrf_token=generate_csrf_token()), 403

    # Validate input
    if not username or not password:
        error_msg = 'Username and password are required.'
        if wants_json:
            return jsonify({'error': error_msg}), 400
        return render_template('login.html', error=error_msg, csrf_token=generate_csrf_token()), 400

    # Check if account is locked
    if is_account_locked(username):
        remaining_time = LOCKOUT_DURATION - (time.time() - locked_accounts.get(username, 0))
        error_msg = f'Account locked due to multiple failed attempts. Try again in {int(remaining_time/60)} minutes.'
        if wants_json:
            return jsonify({'error': error_msg, 'locked_until': remaining_time}), 423
        return render_template('login.html', error=error_msg, csrf_token=generate_csrf_token()), 423

    # Authenticate user
    if username in users and check_password_hash(users[username], password):
        # Reset failed attempts on successful login
        if username in failed_attempts:
            failed_attempts[username] = 0
        if username in locked_accounts:
            del locked_accounts[username]

        # Generate JWT token
        token = generate_token(username)

        # Return appropriate response based on client preference
        if wants_json:
            return jsonify({
                'message': 'Login successful',
                'token': token,
                'username': username,
                'expires_in': JWT_EXPIRATION
            })
        else:
            # For web flow, set secure token as cookie and redirect
            response = make_response(redirect(url_for('index')))
            # Set secure cookie with proper flags
            response.set_cookie(
                'token',
                token,
                max_age=JWT_EXPIRATION,
                httponly=True,
                secure=request.is_secure,  # Only send over HTTPS in production
                samesite='Lax'  # CSRF protection
            )
            return response
    else:
        # Record failed attempt
        record_failed_attempt(username)

        error_msg = 'Invalid username or password.'
        if wants_json:
            return jsonify({'error': error_msg}), 401
        return render_template('login.html', error=error_msg, csrf_token=generate_csrf_token()), 401

@auth.route('/logout')
def logout():
    # Clear session data
    session.clear()

    # Create response and clear the token cookie with proper security
    response = make_response(redirect(url_for('auth.login')))
    response.delete_cookie('token', path='/', domain=None)

    # Add security headers
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

def login_required(view):
    @wraps(view)
    def wrapped_view(*args, **kwargs):
        token = request.cookies.get('token')

        if not token:
            return redirect(url_for('auth.login'))

        try:
            # Decode and validate token
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])

            # Check token expiration
            if 'exp' in payload:
                exp_timestamp = payload['exp']
                if datetime.datetime.now(datetime.timezone.utc).timestamp() > exp_timestamp:
                    # Token expired, redirect to login
                    response = make_response(redirect(url_for('auth.login')))
                    response.delete_cookie('token')
                    return response

            # Token is valid, continue to the view
            return view(*args, **kwargs)

        except jwt.ExpiredSignatureError:
            # Token expired
            response = make_response(redirect(url_for('auth.login')))
            response.delete_cookie('token')
            return response
        except jwt.InvalidTokenError:
            # Token is invalid, redirect to login
            response = make_response(redirect(url_for('auth.login')))
            response.delete_cookie('token')
            return response
        except Exception:
            # Any other error, redirect to login
            response = make_response(redirect(url_for('auth.login')))
            response.delete_cookie('token')
            return response

    return wrapped_view