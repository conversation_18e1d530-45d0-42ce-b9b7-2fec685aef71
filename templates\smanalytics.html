<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>Social Media Analytics</title>
  <!-- Tailwind CSS -->
  {% include 'imports.html' %}

  <script>
    tailwind.config = {
      darkMode: 'class',
    }
  </script>
  <!-- Add Confetti.js -->
  <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.4.0/dist/confetti.browser.min.js"></script>
  <!-- Add Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    body {
      visibility: hidden;
    }
    #app {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    /* Hide scrollbars for all elements */
    * {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    *::-webkit-scrollbar {
      display: none;
    }
    /* Sidebar sticky and scrollable */
    #sidebar {
      position: sticky;
      top: 0;
      height: 100vh;
      overflow-y: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    
    /* Animation Keyframes */
    @keyframes fadeInScaleUp {
      from { opacity: 0; transform: scale(0.95); }
      to { opacity: 1; transform: scale(1); }
    }

    @keyframes slideInFromLeft {
      from { opacity: 0; transform: translateX(-30px); }
      to { opacity: 1; transform: translateX(0); }
    }

    @keyframes slideUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeInPop {
      from { opacity: 0; transform: scale(0.85); }
      to { opacity: 1; transform: scale(1); }
    }

    @keyframes rotateInFade {
      from { opacity: 0; transform: rotate(-10deg) scale(0.9); }
      to { opacity: 1; transform: rotate(0deg) scale(1); }
    }

    /* Base animation class */
    .animated-on-load {
      opacity: 0; /* Start hidden */
      animation-fill-mode: forwards; /* Retain styles from the last keyframe */
    }

    /* Specific animation classes */
    .animate-total-revenue { animation-name: fadeInScaleUp; animation-duration: 1800ms; animation-timing-function: ease-out; animation-delay: 100ms; }
    .animate-conversation-analytics { animation-name: slideInFromLeft; animation-duration: 1500ms; animation-timing-function: ease-out; animation-delay: 200ms; }
    
    .animate-stat-card { animation-name: slideUp; animation-duration: 1200ms; animation-timing-function: ease-in-out; }

    .animate-ai-human-response { animation-name: slideUp; animation-duration: 2400ms; animation-timing-function: ease-out; animation-delay: 300ms; }
    .animate-language-analytics { animation-name: fadeInPop; animation-duration: 1500ms; animation-timing-function: ease-in; animation-delay: 500ms; }
    .animate-product-analytics { animation-name: rotateInFade; animation-duration: 2100ms; animation-timing-function: ease-out; animation-delay: 600ms; }
    .animate-total-tips-chart { animation-name: slideUp; animation-duration: 1800ms; animation-timing-function: ease-in-out; animation-delay: 700ms; }
    .animate-tip-analytics-graph { animation-name: fadeInScaleUp; animation-duration: 3000ms; animation-timing-function: ease-in-out; animation-delay: 800ms; }
  </style>
</head>

<body class="light">
  <!-- Loading Overlay -->
  {% include 'components/loading.html' %}

  <!-- Application Container -->
  <div id="app">
    <!-- Main Grid Layout -->
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
      <!-- Sidebar -->
      {% include 'sidebar.html' %}

      <!-- Main Content -->
      <div class="flex flex-col">
        <header
          class="card flex h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
          <style>
            .uk-navbar-item {
              height: 60px !important;
              min-height: 60px !important;
            }
          </style>
          <div class="flex items-center gap-2 px-4 pl-0">
            <button id="toggle-btn" style="margin-left: 8px;"
              class="opacity-100 transition-opacity duration-300 focus:outline-none"
              style="background-color: transparent !important;">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-panel-left">
                <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                <path d="M9 3v18"></path>
              </svg>
            </button>
            <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
              style="background-color: var(--border-color);"></div>
            <nav aria-label="breadcrumb">
              <ol
                    class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                    <div class="menubar" role="menubar">
                        <div class="menubar-indicator"></div>
                        <a href="/sales" role="menuitem">Sales Report</a>
                        <a href="/pmsanalytics" role="menuitem">PMS Analytics</a>
                        <a href="/googleana" role="menuitem">Google Analytics</a>
                        <a href="/smanalytics" role="menuitem" class="active">SM Analytics</a>
                        <a href="/status" role="menuitem">Status Report</a>
                    </div>
                </ol>
            </nav>
          </div>
          {% include 'topright.html' %}
        </header>

        <!-- Main Page Content -->
        <div class="p-4 h-[calc(100vh-60px)] overflow-y-auto">
          <div class="space-y-4">
            <!-- First Row - Container 1 with 2 side containers -->
            <div class="flex gap-4">              <!-- First Container -->
              <div class="card w-64 h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-xl shadow-sm flex-shrink-0 flex flex-col">
                <!-- WhatsApp Icon at the top -->
                <div class="flex items-center justify-center mb-4">
                  <img src="/static/vector_icons/whatsapp.png" alt="WhatsApp" class="w-16 h-16 object-contain rounded-lg">
                </div>
                <!-- Additional content space -->
                <div class="flex-1">
                  <!-- Content can be added here -->
                </div>
              </div>
              
              <!-- Right Side Container 1 - AI-Human Response Analytics -->
              <div class="card flex-[2.5] h-[calc((100vh-140px)/3*1.2)] border border-gray-300 rounded-lg shadow-sm text-card-fore overflow-hidden animated-on-load animate-ai-human-response">
                <div class="p-4 flex flex-row items-center justify-between space-y-0">
                  <h3 class="text-lg font-semibold mt-[-4px]">AI-Human Response Analytics</h3>
                  
                  <div class="flex items-center gap-4">
                    <div class="flex items-center gap-1">
                      <span class="inline-block w-2.5 h-2.5 rounded-sm bg-cyan-500 mt-[-4px]"></span>
                      <span class="text-xs mt-[-4px]">AI-powered responses</span>
                    </div>
                    <div class="flex items-center gap-1 mr-1">
                      <span class="inline-block w-2.5 h-2.5 rounded-sm bg-rose-500 mt-[-4px]"></span>
                      <span class="text-xs mt-[-4px]">Human agent responses</span>
                    </div>
                  </div>
                </div>
                <div class="px-4 pb-4" style="height: calc(100% - 60px);">
                  <div class="chart-container h-full w-full" style="margin-top: 5px;">
                    <canvas id="salesOverviewChart"
                      style="display: block; box-sizing: border-box; height: 100%; width: 100%;"></canvas>
                  </div>
                </div>
              </div>
              
              <!-- Right Side Container 2 -->
              <div class="card flex-[1.5] h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-lg shadow-sm">
                <!-- Box container placeholder -->
                <div class="text-center text-gray-500">
                  Side Box 2
                </div>
              </div>
            </div>
            
            <!-- Second Container -->
            <div class="card w-64 h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-lg shadow-sm">
              <!-- Box container placeholder -->
              <div class="text-center text-gray-500">
                Container Box 2
              </div>
            </div>
            
            <!-- Third Container -->
            <div class="card w-64 h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-lg shadow-sm">
              <!-- Box container placeholder -->
              <div class="text-center text-gray-500">
                Container Box 3
              </div>
            </div>
            
            <!-- Fourth Container -->
            <div class="card w-64 h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-lg shadow-sm">
              <!-- Box container placeholder -->
              <div class="text-center text-gray-500">
                Container Box 4
              </div>
            </div>
            
            <!-- Fifth Container -->
            <div class="card w-64 h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-lg shadow-sm">
              <!-- Box container placeholder -->
              <div class="text-center text-gray-500">
                Container Box 5
              </div>
            </div>
            
            <!-- Sixth Container -->
            <div class="card w-64 h-[calc((100vh-140px)/3*1.2)] p-4 border border-gray-300 rounded-lg shadow-sm">
              <!-- Box container placeholder -->
              <div class="text-center text-gray-500">
                Container Box 6
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </div>
    
  <!-- Chart.js functionality for AI-Human Response Analytics -->
  <script>
    async function fetchAIvsManualData() {
      try {
        console.log("FETCHAIVSMANUALDATA")
        const response = await fetch('/aivsmanual');
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        return data;
      } catch (error) {
        console.error('Error fetching AI vs Manual data:', error);
        return null;
      }
    }

    function calculateStepSize(maxValue) {
      return Math.ceil(maxValue / 5);
    }

    function createCustomTooltipForTips(context) {
      // Create tooltip element if it doesn't exist
      let tooltipEl = document.getElementById('tips-chart-tooltip');

      if (!tooltipEl) {
        tooltipEl = document.createElement('div');
        tooltipEl.id = 'tips-chart-tooltip';
        tooltipEl.style.position = 'absolute';
        tooltipEl.style.pointerEvents = 'none';
        tooltipEl.style.transition = 'left 0.3s ease, top 0.3s ease, opacity 0.3s ease'; // Smoother animation
        tooltipEl.style.zIndex = 1000;
        document.body.appendChild(tooltipEl);
      }

      // Hide tooltip when not active
      const tooltipModel = context.tooltip;
      if (tooltipModel.opacity === 0) {
        tooltipEl.style.opacity = 0;
        return;
      }

      // Set tooltip content
      if (tooltipModel.body) {
        const dataPoint = tooltipModel.dataPoints[0];
        const value = dataPoint.raw;
        const label = tooltipModel.title[0]; // Day label (Mon, Tue, etc.)
        
        // Get the correct color from the specific dataset being hovered
        const datasetIndex = dataPoint.datasetIndex;
        const chartBarColor = context.chart.config.data.datasets[datasetIndex].backgroundColor;
        const datasetLabel = context.chart.config.data.datasets[datasetIndex].label || '';
        
        // Using the same color scheme as externalCustomTooltip function
        tooltipEl.innerHTML = `
          <div class="flex items-center rounded-md px-1.5 py-1 shadow-md "
               style="background-color: var(--dropdown-bg); border: 1px solid var(--dropdown-border, #dee2e6); font-weight:300; color: var(--dropdown-text, #111827);">
            <span class="w-3 h-3 rounded-sm mr-2 flex-shrink-0" style="background-color: ${chartBarColor}"></span>
            <span class="text-sm">${label}</span>
            <span class="ml-4 text-sm font-medium">${value}</span>
          </div>
        `;
      }

      // Position the tooltip with improved positioning
      const position = context.chart.canvas.getBoundingClientRect();
      const tooltipWidth = tooltipEl.offsetWidth;
      
      // Calculate position (center aligned with bar)
      const positionX = position.left + window.pageXOffset + tooltipModel.caretX - (tooltipWidth / 2);
      const positionY = position.top + window.pageYOffset + tooltipModel.caretY - 40; // Position above the bar
      
      // Apply position with smooth transition
      tooltipEl.style.opacity = 1;
      tooltipEl.style.left = positionX + 'px';
      tooltipEl.style.top = positionY + 'px';
    }

    async function updateChart(chart) {
      const interactionData = await fetchAIvsManualData();
      if (!interactionData) return;

      const aiData = interactionData.ai_inter[0];
      const manualData = interactionData.manual_inter[0];

      const aiInteractionsData = [
        aiData.mon, aiData.tue, aiData.wed, aiData.thu,
        aiData.fri, aiData.sat, aiData.sun
      ];

      const manualInteractionsData = [
        manualData.mon, manualData.tue, manualData.wed, manualData.thu,
        manualData.fri, manualData.sat, manualData.sun
      ];

      chart.data.datasets[0].data = aiInteractionsData;
      chart.data.datasets[1].data = manualInteractionsData;
      chart.update();
    }

    async function initializeChart() {
      const interactionData = await fetchAIvsManualData();
      if (!interactionData) return;

      const aiData = interactionData.ai_inter[0];
      const manualData = interactionData.manual_inter[0];

      const aiInteractionsData = [
        aiData.mon, aiData.tue, aiData.wed, aiData.thu,
        aiData.fri, aiData.sat, aiData.sun
      ];

      const manualInteractionsData = [
        manualData.mon, manualData.tue, manualData.wed, manualData.thu,
        manualData.fri, manualData.sat, manualData.sun
      ];

      const maxDataValue = Math.max(...aiInteractionsData, ...manualInteractionsData);
      const stepSize = calculateStepSize(maxDataValue);

      // Check if dark mode is active
      const isPureBlack = document.body.classList.contains('pure-black');

      // Set grid color based on theme - use white with opacity for dark mode
      const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

      const interactionChartData = {
        labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        datasets: [
          {
            label: 'AI help (Automated)',
            data: aiInteractionsData,
            backgroundColor: '#e21d48', // Solid color
            borderRadius: {
              topLeft: 5,
              topRight: 5,
              bottomLeft: 5,
              bottomRight: 5
            },
            borderSkipped: false
          },
          {
            label: 'Staff help (manual)',
            data: manualInteractionsData,
            backgroundColor: '#5ea5f6', // Solid color
            borderRadius: {
              topLeft: 5,
              topRight: 5,
              bottomLeft: 5,
              bottomRight: 5
            },
            borderSkipped: false
          }
        ]
      };

      const ctx = document.getElementById('salesOverviewChart').getContext('2d');
      const interactionChart = new Chart(ctx, {
        type: 'bar',
        data: interactionChartData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          barPercentage: 0.9,
          categoryPercentage: 0.8,
          scales: {
            x: {
              grid: {
                display: false,
                color: 'transparent'
              },
              border: { display: false }
            },
            y: {
              beginAtZero: true,
              grid: {
                color: gridColor,
                borderDash: [5, 5]
              },
              border: { display: false },
              ticks: {
                stepSize: stepSize
              }
            }
          },
          plugins: {
            tooltip: {
              enabled: false,
              external: createCustomTooltipForTips
            },
            legend: {
              display: false // Hide the default legend
            }
          }
        }
      });

      // Add theme change observer to update grid colors when theme changes
      const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const isDarkMode = document.body.classList.contains('pure-black');
            const newGridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            // Update grid color
            interactionChart.options.scales.y.grid.color = newGridColor;
            interactionChart.update();
          }
        });
      });

      observer.observe(document.body, {
        attributes: true
      });

      // Set interval to update the chart every minute
      setInterval(() => {
        updateChart(interactionChart);
      }, 60000);
    }

    document.addEventListener('DOMContentLoaded', initializeChart);
  </script>

  <!-- Custom tooltip styles -->
  <style>
    #tips-chart-tooltip {
      background: var(--dropdown-bg, #ffffff);
      border: 1px solid var(--dropdown-border, #dee2e6);
      border-radius: 6px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      font-size: 12px;
      z-index: 1000;
    }
  </style>

</body>
</html>