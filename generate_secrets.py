#!/usr/bin/env python3
"""
Security Secret Generator for Guest <PERSON><PERSON>
Generates secure secrets for JWT, Flask, and other security configurations.
"""

import secrets
import string
import hashlib
import os
from datetime import datetime

def generate_jwt_secret(length=32):
    """Generate a secure JWT secret"""
    return secrets.token_urlsafe(length)

def generate_flask_secret(length=32):
    """Generate a secure Flask secret key"""
    return secrets.token_urlsafe(length)

def generate_strong_password(length=16):
    """Generate a strong password with mixed characters"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    
    # Ensure password meets complexity requirements
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*" for c in password)
    
    if not all([has_upper, has_lower, has_digit, has_special]):
        # Regenerate if requirements not met
        return generate_strong_password(length)
    
    return password

def generate_api_key(prefix="", length=32):
    """Generate an API key with optional prefix"""
    key = secrets.token_urlsafe(length)
    return f"{prefix}{key}" if prefix else key

def hash_password(password):
    """Generate a secure hash of a password"""
    from werkzeug.security import generate_password_hash
    return generate_password_hash(password)

def main():
    print("🔐 Guest Genius Security Secret Generator")
    print("=" * 50)
    print()
    
    # Generate secrets
    jwt_secret = generate_jwt_secret()
    flask_secret = generate_flask_secret()
    admin_password = generate_strong_password()
    
    print("Generated Secure Secrets:")
    print("-" * 30)
    print(f"JWT_SECRET={jwt_secret}")
    print(f"FLASK_SECRET_KEY={flask_secret}")
    print(f"ADMIN_PASSWORD={admin_password}")
    print()
    
    # Additional security information
    print("Security Information:")
    print("-" * 20)
    print(f"JWT Secret Length: {len(jwt_secret)} characters")
    print(f"Flask Secret Length: {len(flask_secret)} characters")
    print(f"Admin Password Length: {len(admin_password)} characters")
    print(f"Password Hash: {hash_password(admin_password)[:50]}...")
    print()
    
    # Save to file option
    save_option = input("Save secrets to .env.secure file? (y/n): ").lower().strip()
    if save_option == 'y':
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f".env.secure_{timestamp}"
        
        with open(filename, 'w') as f:
            f.write("# Generated Security Secrets\n")
            f.write(f"# Generated on: {datetime.now().isoformat()}\n")
            f.write("# IMPORTANT: Copy these to your .env file and delete this file\n\n")
            f.write(f"JWT_SECRET={jwt_secret}\n")
            f.write(f"FLASK_SECRET_KEY={flask_secret}\n")
            f.write(f"ADMIN_PASSWORD={admin_password}\n")
            f.write(f"\n# Password hash for verification:\n")
            f.write(f"# {hash_password(admin_password)}\n")
        
        print(f"✅ Secrets saved to {filename}")
        print("⚠️  Remember to:")
        print("   1. Copy secrets to your .env file")
        print("   2. Delete the .env.secure_* file")
        print("   3. Never commit secrets to version control")
    
    print()
    print("🛡️  Security Reminders:")
    print("   • Use these secrets in your .env file")
    print("   • Never share or commit secrets")
    print("   • Rotate secrets regularly")
    print("   • Use HTTPS in production")
    print("   • Enable proper logging and monitoring")

if __name__ == "__main__":
    main()
