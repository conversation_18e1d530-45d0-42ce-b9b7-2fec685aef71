# ==================== SECURITY CONFIGURATION ====================
# IMPORTANT: Copy this file to .env and update with your secure values
# Never commit .env to version control!

# ==================== AUTHENTICATION & SECURITY ====================
# Generate a strong admin password (minimum 12 characters, mixed case, numbers, symbols)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=YourSecurePassword123!@#

# Generate a secure JWT secret (use: python -c "import secrets; print(secrets.token_urlsafe(32))")
JWT_SECRET=your-secure-jwt-secret-here-32-chars-minimum

# JWT token expiration in seconds (default: 10 hours = 36000)
JWT_EXPIRATION=36000

# Flask secret key for sessions (use: python -c "import secrets; print(secrets.token_urlsafe(32))")
FLASK_SECRET_KEY=your-secure-flask-secret-here-32-chars-minimum

# ==================== DATABASE CONFIGURATION ====================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key

# ==================== COMMUNICATION SERVICES ====================
# Twilio Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=whatsapp:+**********
FROM_WHATSAPP_NUMBER=whatsapp:+**********

# WhatsApp Business API (if using)
access_token=your-whatsapp-access-token
phone_number_id=your-phone-number-id

# ==================== AI SERVICES ====================
OPENAI_API_KEY=sk-your-openai-api-key
GROQ_API_KEY=gsk_your-groq-api-key
GEMINI_API_KEY=your-gemini-api-key

# ==================== APPLICATION ENVIRONMENT ====================
FLASK_ENV=production
DEBUG=False

# ==================== SECURITY NOTES ====================
# 1. Use strong, unique passwords for all accounts
# 2. Regularly rotate API keys and secrets
# 3. Enable 2FA on all service accounts
# 4. Monitor API usage and set up alerts
# 5. Use environment-specific configurations
# 6. Never share credentials in plain text
# 7. Use a password manager for credential storage
