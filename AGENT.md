# Agent Guidelines

## Technology Stack
- **Framework**: Flask 3.0.3
- **Dependencies**: <PERSON>wi<PERSON>, Supabase, JWT, python-dotenv, requests, colorama
- **Database**: Supabase (PostgreSQL)
- **Auth**: JWT tokens with cookie-based sessions

## Build/Test Commands
```bash
python app.py                    # Run main Flask application
python Bots/Whatsapp/English/app.py  # Run WhatsApp bot
pip install -r requirements.txt  # Install dependencies
```

## Code Style Guidelines
- **Imports**: Group by standard library, third-party, local modules with blank lines between
- **Naming**: snake_case for functions/variables, UPPER_CASE for constants, descriptive names
- **Functions**: Use decorators (@login_required, @check_request_allowed) for route protection
- **Error Handling**: Use try/except with detailed logging, return JSON error responses with status codes
- **Logging**: Use print_colored() for colored terminal output, logger for file logging
- **Constants**: Define template IDs, URLs, and config at module top
- **Authentication**: Use JWT tokens, validate with login_required decorator
- **API Responses**: Return jsonify() with consistent error/success format
- **Environment**: Load config from .env using python-dotenv, use os.getenv() with defaults